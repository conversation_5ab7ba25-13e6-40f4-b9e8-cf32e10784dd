{"_from": "@ungap/structured-clone@^1.2.0", "_id": "@ungap/structured-clone@1.3.0", "_inBundle": false, "_integrity": "sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g==", "_location": "/@ungap/structured-clone", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@ungap/structured-clone@^1.2.0", "name": "@ungap/structured-clone", "escapedName": "@ungap%2fstructured-clone", "scope": "@ungap", "rawSpec": "^1.2.0", "saveSpec": null, "fetchSpec": "^1.2.0"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.3.0.tgz", "_shasum": "d06bbb384ebcf6c505fde1c3d0ed4ddffe0aaff8", "_spec": "@ungap/structured-clone@^1.2.0", "_where": "/Users/<USER>/Desktop/test/node_modules/eslint", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/ungap/structured-clone/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A structuredClone polyfill", "devDependencies": {"@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-terser": "^0.4.4", "ascjs": "^6.0.3", "c8": "^10.1.3", "coveralls": "^3.1.1", "rollup": "^4.31.0"}, "directories": {"test": "test"}, "exports": {".": {"import": "./esm/index.js", "default": "./cjs/index.js"}, "./json": {"import": "./esm/json.js", "default": "./cjs/json.js"}, "./package.json": "./package.json"}, "homepage": "https://github.com/ungap/structured-clone#readme", "keywords": ["recursion", "structured", "clone", "algorithm"], "license": "ISC", "main": "./cjs/index.js", "module": "./esm/index.js", "name": "@ungap/structured-clone", "repository": {"type": "git", "url": "git+https://github.com/ungap/structured-clone.git"}, "scripts": {"build": "npm run cjs && npm run rollup:json && npm run test", "cjs": "ascjs esm cjs", "coverage": "c8 report --reporter=text-lcov > ./coverage/lcov.info", "rollup:json": "rollup --config rollup/json.config.js", "test": "c8 node test/index.js"}, "sideEffects": false, "type": "module", "version": "1.3.0"}
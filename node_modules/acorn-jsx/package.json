{"_from": "acorn-jsx@^5.3.2", "_id": "acorn-jsx@5.3.2", "_inBundle": false, "_integrity": "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==", "_location": "/acorn-jsx", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "acorn-jsx@^5.3.2", "name": "acorn-jsx", "escapedName": "acorn-jsx", "rawSpec": "^5.3.2", "saveSpec": null, "fetchSpec": "^5.3.2"}, "_requiredBy": ["/espree"], "_resolved": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "_shasum": "7ed5bb55908b3b2f1bc55c6af1653bada7f07937", "_spec": "acorn-jsx@^5.3.2", "_where": "/Users/<USER>/Desktop/test/node_modules/espree", "bugs": {"url": "https://github.com/acornjs/acorn-jsx/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Modern, fast React.js JSX parser", "devDependencies": {"acorn": "^8.0.1"}, "homepage": "https://github.com/acornjs/acorn-jsx", "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://rreverser.com/"}], "name": "acorn-jsx", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/acornjs/acorn-jsx.git"}, "scripts": {"test": "node test/run.js"}, "version": "5.3.2"}
{"_from": "@humanwhocodes/object-schema@^2.0.3", "_id": "@humanwhocodes/object-schema@2.0.3", "_inBundle": false, "_integrity": "sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==", "_location": "/@humanwhocodes/object-schema", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@humanwhocodes/object-schema@^2.0.3", "name": "@humanwhocodes/object-schema", "escapedName": "@humanwhocodes%2fobject-schema", "scope": "@humanwhocodes", "rawSpec": "^2.0.3", "saveSpec": null, "fetchSpec": "^2.0.3"}, "_requiredBy": ["/@humanwhocodes/config-array"], "_resolved": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz", "_shasum": "4a2868d75d6d6963e423bcf90b7fd1be343409d3", "_spec": "@humanwhocodes/object-schema@^2.0.3", "_where": "/Users/<USER>/Desktop/test/node_modules/@humanwhocodes/config-array", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/humanwhocodes/object-schema/issues"}, "bundleDependencies": false, "deprecated": "Use @eslint/object-schema instead", "description": "An object schema merger/validator", "devDependencies": {"chai": "^4.2.0", "eslint": "^5.13.0", "mocha": "^5.2.0"}, "directories": {"test": "tests"}, "files": ["src", "LICENSE", "README.md"], "homepage": "https://github.com/humanwhocodes/object-schema#readme", "keywords": ["object", "validation", "schema", "merge"], "license": "BSD-3-<PERSON><PERSON>", "main": "src/index.js", "name": "@humanwhocodes/object-schema", "repository": {"type": "git", "url": "git+https://github.com/humanwhocodes/object-schema.git"}, "scripts": {"test": "mocha tests/"}, "version": "2.0.3"}
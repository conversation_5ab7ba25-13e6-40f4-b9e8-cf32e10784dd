{"_from": "@humanwhocodes/config-array@^0.13.0", "_id": "@humanwhocodes/config-array@0.13.0", "_inBundle": false, "_integrity": "sha512-DZLEEqFWQFiyK6h5YIeynKx7JlvCYWL0cImfSRXZ9l4Sg2efkFGTuFf6vzXjK1cq6IYkU+Eg/JizXw+TD2vRNw==", "_location": "/@humanwhocodes/config-array", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@humanwhocodes/config-array@^0.13.0", "name": "@humanwhocodes/config-array", "escapedName": "@humanwhocodes%2fconfig-array", "scope": "@humanwhocodes", "rawSpec": "^0.13.0", "saveSpec": null, "fetchSpec": "^0.13.0"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.13.0.tgz", "_shasum": "fb907624df3256d04b9aa2df50d7aa97ec648748", "_spec": "@humanwhocodes/config-array@^0.13.0", "_where": "/Users/<USER>/Desktop/test/node_modules/eslint", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/humanwhocodes/config-array/issues"}, "bundleDependencies": false, "dependencies": {"@humanwhocodes/object-schema": "^2.0.3", "debug": "^4.3.1", "minimatch": "^3.0.5"}, "deprecated": "Use @eslint/config-array instead", "description": "Glob-based configuration matching.", "devDependencies": {"@nitpik/javascript": "0.4.0", "@nitpik/node": "0.0.5", "chai": "4.3.10", "eslint": "8.52.0", "esm": "3.2.25", "lint-staged": "15.0.2", "mocha": "6.2.3", "nyc": "15.1.0", "rollup": "3.28.1", "yorkie": "2.0.0"}, "engines": {"node": ">=10.10.0"}, "files": ["api.js", "LICENSE", "README.md"], "gitHooks": {"pre-commit": "lint-staged"}, "homepage": "https://github.com/humanwhocodes/config-array#readme", "keywords": ["configuration", "configarray", "config file"], "license": "Apache-2.0", "lint-staged": {"*.js": ["eslint --fix --ignore-pattern '!.eslintrc.js'"]}, "main": "api.js", "name": "@humanwhocodes/config-array", "repository": {"type": "git", "url": "git+https://github.com/humanwhocodes/config-array.git"}, "scripts": {"build": "rollup -c", "format": "nitpik", "lint": "eslint *.config.js src/*.js tests/*.js", "lint:fix": "eslint --fix *.config.js src/*.js tests/*.js", "prepublish": "npm run build", "test": "mocha -r esm tests/ --recursive", "test:coverage": "nyc --include src/*.js npm run test"}, "version": "0.13.0"}
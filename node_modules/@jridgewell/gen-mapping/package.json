{"_from": "@jridgewell/gen-mapping@^0.3.5", "_id": "@jridgewell/gen-mapping@0.3.8", "_inBundle": false, "_integrity": "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==", "_location": "/@jridgewell/gen-mapping", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@jridgewell/gen-mapping@^0.3.5", "name": "@jridgewell/gen-mapping", "escapedName": "@jridgewell%2fgen-mapping", "scope": "@jridgewell", "rawSpec": "^0.3.5", "saveSpec": null, "fetchSpec": "^0.3.5"}, "_requiredBy": ["/@ampproject/remapping", "/@babel/generator"], "_resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "_shasum": "4f0e06362e01362f823d348f1872b08f666d8142", "_spec": "@jridgewell/gen-mapping@^0.3.5", "_where": "/Users/<USER>/Desktop/test/node_modules/@ampproject/remapping", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jridgewell/gen-mapping/issues"}, "bundleDependencies": false, "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "deprecated": false, "description": "Generate source maps", "devDependencies": {"@rollup/plugin-typescript": "8.3.2", "@types/mocha": "9.1.1", "@types/node": "17.0.29", "@typescript-eslint/eslint-plugin": "5.21.0", "@typescript-eslint/parser": "5.21.0", "benchmark": "2.1.4", "c8": "7.11.2", "eslint": "8.14.0", "eslint-config-prettier": "8.5.0", "mocha": "9.2.2", "npm-run-all": "4.1.5", "prettier": "2.6.2", "rollup": "2.70.2", "tsx": "4.7.1", "typescript": "4.6.3"}, "engines": {"node": ">=6.0.0"}, "exports": {".": [{"types": "./dist/types/gen-mapping.d.ts", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js", "import": "./dist/gen-mapping.mjs"}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "files": ["dist"], "homepage": "https://github.com/jridgewell/gen-mapping#readme", "keywords": ["source", "map"], "license": "MIT", "main": "dist/gen-mapping.umd.js", "module": "dist/gen-mapping.mjs", "name": "@jridgewell/gen-mapping", "repository": {"type": "git", "url": "git+https://github.com/jridgewell/gen-mapping.git"}, "scripts": {"benchmark": "run-s build:rollup benchmark:*", "benchmark:install": "cd benchmark && npm install", "benchmark:only": "node benchmark/index.mjs", "build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint test:only", "test:debug": "mocha --inspect-brk", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "c8 mocha", "test:watch": "mocha --watch"}, "types": "dist/types/gen-mapping.d.ts", "version": "0.3.8"}
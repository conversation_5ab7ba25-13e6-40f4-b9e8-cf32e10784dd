{"_from": "@eslint-community/regexpp@^4.6.1", "_id": "@eslint-community/regexpp@4.12.1", "_inBundle": false, "_integrity": "sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==", "_location": "/@eslint-community/regexpp", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@eslint-community/regexpp@^4.6.1", "name": "@eslint-community/regexpp", "escapedName": "@eslint-community%2fregexpp", "scope": "@eslint-community", "rawSpec": "^4.6.1", "saveSpec": null, "fetchSpec": "^4.6.1"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "_shasum": "cfc6cffe39df390a3841cde2abccf92eaa7ae0e0", "_spec": "@eslint-community/regexpp@^4.6.1", "_where": "/Users/<USER>/Desktop/test/node_modules/eslint", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Regular expression parser for ECMAScript.", "devDependencies": {"@eslint-community/eslint-plugin-mysticatea": "^15.5.1", "@rollup/plugin-node-resolve": "^14.1.0", "@types/eslint": "^8.44.3", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/node": "^12.20.55", "dts-bundle": "^0.7.3", "eslint": "^8.50.0", "js-tokens": "^8.0.2", "jsdom": "^19.0.0", "mocha": "^9.2.2", "npm-run-all2": "^6.2.2", "nyc": "^14.1.1", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.9.1", "typescript": "~5.0.2"}, "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "files": ["index.*"], "homepage": "https://github.com/eslint-community/regexpp#readme", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "license": "MIT", "main": "index", "name": "@eslint-community/regexpp", "repository": {"type": "git", "url": "git+https://github.com/eslint-community/regexpp.git"}, "scripts": {"build": "run-s build:*", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:rollup": "rollup -c", "build:tsc": "tsc --module es2015", "clean": "rimraf .temp index.*", "debug": "mocha --require ts-node/register/transpile-only \"test/*.ts\" --reporter dot --timeout 10000", "lint": "eslint . --ext .ts", "postversion": "git push && git push --tags", "prebuild": "npm run -s clean", "preversion": "npm test && npm run -s build", "prewatch": "npm run -s clean", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "update:test": "ts-node scripts/update-fixtures.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl"}, "version": "4.12.1"}
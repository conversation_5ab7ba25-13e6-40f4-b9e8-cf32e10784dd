{"_from": "@nodelib/fs.walk@^1.2.8", "_id": "@nodelib/fs.walk@1.2.8", "_inBundle": false, "_integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==", "_location": "/@nodelib/fs.walk", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@nodelib/fs.walk@^1.2.8", "name": "@nodelib/fs.walk", "escapedName": "@nodelib%2ffs.walk", "scope": "@nodelib", "rawSpec": "^1.2.8", "saveSpec": null, "fetchSpec": "^1.2.8"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "_shasum": "e95737e8bb6746ddedf69c556953494f196fe69a", "_spec": "@nodelib/fs.walk@^1.2.8", "_where": "/Users/<USER>/Desktop/test/node_modules/eslint", "bundleDependencies": false, "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "deprecated": false, "description": "A library for efficiently walking a directory recursively", "devDependencies": {"@nodelib/fs.macchiato": "1.0.4"}, "engines": {"node": ">= 8"}, "files": ["out/**", "!out/**/*.map", "!out/**/*.spec.*", "!out/**/tests/**"], "gitHead": "1e5bad48565da2b06b8600e744324ea240bf49d8", "keywords": ["NodeLib", "fs", "FileSystem", "file system", "walk", "scanner", "crawler"], "license": "MIT", "main": "out/index.js", "name": "@nodelib/fs.walk", "repository": {"type": "git", "url": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.walk"}, "scripts": {"build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "rimraf {tsconfig.tsbuildinfo,out}", "compile": "tsc -b .", "compile:watch": "tsc -p . --watch --sourceMap", "lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "watch": "npm run clean && npm run compile:watch"}, "typings": "out/index.d.ts", "version": "1.2.8"}
{"_from": "@nodelib/fs.stat@2.0.5", "_id": "@nodelib/fs.stat@2.0.5", "_inBundle": false, "_integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==", "_location": "/@nodelib/fs.stat", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@nodelib/fs.stat@2.0.5", "name": "@nodelib/fs.stat", "escapedName": "@nodelib%2ffs.stat", "scope": "@nodelib", "rawSpec": "2.0.5", "saveSpec": null, "fetchSpec": "2.0.5"}, "_requiredBy": ["/@nodelib/fs.scandir"], "_resolved": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "_shasum": "5bd262af94e9d25bd1e71b05deed44876a222e8b", "_spec": "@nodelib/fs.stat@2.0.5", "_where": "/Users/<USER>/Desktop/test/node_modules/@nodelib/fs.scandir", "bundleDependencies": false, "deprecated": false, "description": "Get the status of a file with some features", "devDependencies": {"@nodelib/fs.macchiato": "1.0.4"}, "engines": {"node": ">= 8"}, "files": ["out/**", "!out/**/*.map", "!out/**/*.spec.*"], "gitHead": "d6a7960d5281d3dd5f8e2efba49bb552d090f562", "keywords": ["NodeLib", "fs", "FileSystem", "file system", "stat"], "license": "MIT", "main": "out/index.js", "name": "@nodelib/fs.stat", "repository": {"type": "git", "url": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.stat"}, "scripts": {"build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "rimraf {tsconfig.tsbuildinfo,out}", "compile": "tsc -b .", "compile:watch": "tsc -p . --watch --sourceMap", "lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "watch": "npm run clean && npm run compile:watch"}, "typings": "out/index.d.ts", "version": "2.0.5"}
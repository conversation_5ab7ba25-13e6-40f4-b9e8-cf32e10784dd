{"_from": "@babel/generator@^7.27.3", "_id": "@babel/generator@7.27.5", "_inBundle": false, "_integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "_location": "/@babel/generator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/generator@^7.27.3", "name": "@babel/generator", "escapedName": "@babel%2fgenerator", "scope": "@babel", "rawSpec": "^7.27.3", "saveSpec": null, "fetchSpec": "^7.27.3"}, "_requiredBy": ["/@babel/core", "/@babel/traverse"], "_resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.5.tgz", "_shasum": "3eb01866b345ba261b04911020cbe22dd4be8c8c", "_spec": "@babel/generator@^7.27.3", "_where": "/Users/<USER>/Desktop/test/node_modules/@babel/core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "deprecated": false, "description": "Turns an AST into code.", "devDependencies": {"@babel/core": "^7.27.4", "@babel/helper-fixtures": "^7.27.1", "@babel/plugin-transform-typescript": "^7.27.1", "@jridgewell/sourcemap-codec": "^1.4.15", "@types/jsesc": "^2.5.0", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.9.0"}, "files": ["lib"], "homepage": "https://babel.dev/docs/en/next/babel-generator", "license": "MIT", "main": "./lib/index.js", "name": "@babel/generator", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-generator"}, "type": "commonjs", "version": "7.27.5"}
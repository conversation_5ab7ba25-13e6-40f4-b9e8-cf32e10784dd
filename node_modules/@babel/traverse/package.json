{"_from": "@babel/traverse@^7.27.4", "_id": "@babel/traverse@7.27.4", "_inBundle": false, "_integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "_location": "/@babel/traverse", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/traverse@^7.27.4", "name": "@babel/traverse", "escapedName": "@babel%2ftraverse", "scope": "@babel", "rawSpec": "^7.27.4", "saveSpec": null, "fetchSpec": "^7.27.4"}, "_requiredBy": ["/@babel/core", "/@babel/helper-module-imports", "/@babel/helper-module-transforms"], "_resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.4.tgz", "_shasum": "b0045ac7023c8472c3d35effd7cc9ebd638da6ea", "_spec": "@babel/traverse@^7.27.4", "_where": "/Users/<USER>/Desktop/test/node_modules/@babel/core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20traverse%22+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}, "deprecated": false, "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "devDependencies": {"@babel/core": "^7.27.4", "@babel/helper-plugin-test-runner": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-traverse", "license": "MIT", "main": "./lib/index.js", "name": "@babel/traverse", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-traverse"}, "type": "commonjs", "version": "7.27.4"}
{"_from": "@babel/helper-module-transforms@^7.27.3", "_id": "@babel/helper-module-transforms@7.27.3", "_inBundle": false, "_integrity": "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==", "_location": "/@babel/helper-module-transforms", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-module-transforms@^7.27.3", "name": "@babel/helper-module-transforms", "escapedName": "@babel%2fhelper-module-transforms", "scope": "@babel", "rawSpec": "^7.27.3", "saveSpec": null, "fetchSpec": "^7.27.3"}, "_requiredBy": ["/@babel/core"], "_resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "_shasum": "db0bbcfba5802f9ef7870705a7ef8788508ede02", "_spec": "@babel/helper-module-transforms@^7.27.3", "_where": "/Users/<USER>/Desktop/test/node_modules/@babel/core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}, "deprecated": false, "description": "Babel helper functions for implementing ES6 module transformations", "devDependencies": {"@babel/core": "^7.27.3"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-module-transforms", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-module-transforms"}, "type": "commonjs", "version": "7.27.3"}
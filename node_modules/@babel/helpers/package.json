{"_from": "@babel/helpers@^7.27.4", "_id": "@babel/helpers@7.27.6", "_inBundle": false, "_integrity": "sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==", "_location": "/@babel/helpers", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helpers@^7.27.4", "name": "@babel/helpers", "escapedName": "@babel%2fhelpers", "scope": "@babel", "rawSpec": "^7.27.4", "saveSpec": null, "fetchSpec": "^7.27.4"}, "_requiredBy": ["/@babel/core"], "_resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.6.tgz", "_shasum": "6456fed15b2cb669d2d1fabe84b66b34991d812c", "_spec": "@babel/helpers@^7.27.4", "_where": "/Users/<USER>/Desktop/test/node_modules/@babel/core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.6"}, "deprecated": false, "description": "Collection of helper functions used by Babel transforms.", "devDependencies": {"@babel/generator": "^7.27.5", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/parser": "^7.27.5", "regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helpers", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helpers", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helpers"}, "type": "commonjs", "version": "7.27.6"}
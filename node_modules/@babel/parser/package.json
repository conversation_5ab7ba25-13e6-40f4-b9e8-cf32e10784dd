{"# dependencies": "This package doesn't actually have runtime dependencies. @babel/types is only needed for type definitions.", "_from": "@babel/parser@^7.27.4", "_id": "@babel/parser@7.27.5", "_inBundle": false, "_integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "_location": "/@babel/parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/parser@^7.27.4", "name": "@babel/parser", "escapedName": "@babel%2fparser", "scope": "@babel", "rawSpec": "^7.27.4", "saveSpec": null, "fetchSpec": "^7.27.4"}, "_requiredBy": ["/@babel/core", "/@babel/generator", "/@babel/template", "/@babel/traverse", "/@types/babel__core", "/@types/babel__template"], "_resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz", "_shasum": "ed22f871f110aa285a6fd934a0efed621d118826", "_spec": "@babel/parser@^7.27.4", "_where": "/Users/<USER>/Desktop/test/node_modules/@babel/core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bin": {"parser": "bin/babel-parser.js"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A+parser+%28babylon%29%22+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@babel/types": "^7.27.3"}, "deprecated": false, "description": "A JavaScript parser", "devDependencies": {"@babel/code-frame": "^7.27.1", "@babel/helper-check-duplicate-nodes": "^7.27.1", "@babel/helper-fixtures": "^7.27.1", "@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.0.0"}, "files": ["bin", "lib", "typings/babel-parser.d.ts", "index.cjs"], "homepage": "https://babel.dev/docs/en/next/babel-parser", "keywords": ["babel", "javascript", "parser", "tc39", "ecmascript", "@babel/parser"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/parser", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-parser"}, "type": "commonjs", "types": "./typings/babel-parser.d.ts", "version": "7.27.5"}
{"_from": "@babel/compat-data@^7.27.2", "_id": "@babel/compat-data@7.27.5", "_inBundle": false, "_integrity": "sha512-KiRAp/VoJaWkkte84TvUd9qjdbZAdiqyvMxrGl1N6vzFogKmaLgoM3L1kgtLicp2HP5fBJS8JrZKLVIZGVJAVg==", "_location": "/@babel/compat-data", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/compat-data@^7.27.2", "name": "@babel/compat-data", "escapedName": "@babel%2fcompat-data", "scope": "@babel", "rawSpec": "^7.27.2", "saveSpec": null, "fetchSpec": "^7.27.2"}, "_requiredBy": ["/@babel/helper-compilation-targets"], "_resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.5.tgz", "_shasum": "7d0658ec1a8420fc866d1df1b03bea0e79934c82", "_spec": "@babel/compat-data@^7.27.2", "_where": "/Users/<USER>/Desktop/test/node_modules/@babel/helper-compilation-targets", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "deprecated": false, "description": "The compat-data to determine required Babel plugins", "devDependencies": {"@mdn/browser-compat-data": "^6.0.8", "core-js-compat": "^3.41.0", "electron-to-chromium": "^1.5.140"}, "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js", "./overlapping-plugins": "./overlapping-plugins.js", "./plugin-bugfixes": "./plugin-bugfixes.js"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel", "compat-table", "compat-data"], "license": "MIT", "name": "@babel/compat-data", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-compat-data"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.mjs && node ./scripts/build-modules-support.mjs && node ./scripts/build-bugfixes-targets.mjs"}, "type": "commonjs", "version": "7.27.5"}
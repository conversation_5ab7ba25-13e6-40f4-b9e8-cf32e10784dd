{"_from": "@rolldown/pluginutils@1.0.0-beta.19", "_id": "@rolldown/pluginutils@1.0.0-beta.19", "_inBundle": false, "_integrity": "sha512-3FL3mnMbPu0muGOCaKAhhFEYmqv9eTfPSJRJmANrCwtgK8VuxpsZDGK+m0LYAGoyO8+0j5uRe4PeyPDK1yA/hA==", "_location": "/@rolldown/pluginutils", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@rolldown/pluginutils@1.0.0-beta.19", "name": "@rolldown/pluginutils", "escapedName": "@rolldown%2fpluginutils", "scope": "@rolldown", "rawSpec": "1.0.0-beta.19", "saveSpec": null, "fetchSpec": "1.0.0-beta.19"}, "_requiredBy": ["/@vitejs/plugin-react"], "_resolved": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.19.tgz", "_shasum": "fc3b95145a8e7a3bf92754269d8e4f40eea8a244", "_spec": "@rolldown/pluginutils@1.0.0-beta.19", "_where": "/Users/<USER>/Desktop/test/node_modules/@vitejs/plugin-react", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "bundleDependencies": false, "deprecated": false, "devDependencies": {"@types/picomatch": "^4.0.0", "picomatch": "^4.0.2", "tsdown": "0.12.8", "vitest": "^3.0.1"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "files": ["dist"], "homepage": "https://github.com/rolldown/rolldown#readme", "license": "MIT", "main": "./dist/index.cjs", "module": "./dist/index.js", "name": "@rolldown/pluginutils", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/rolldown/rolldown.git", "directory": "packages/pluginutils"}, "scripts": {"build": "tsdown", "test": "vitest --typecheck"}, "type": "module", "types": "./dist/index.d.ts", "version": "1.0.0-beta.19"}
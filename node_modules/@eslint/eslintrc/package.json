{"_from": "@eslint/eslintrc@^2.1.4", "_id": "@eslint/eslintrc@2.1.4", "_inBundle": false, "_integrity": "sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==", "_location": "/@eslint/eslintrc", "_phantomChildren": {"type-fest": "0.20.2"}, "_requested": {"type": "range", "registry": true, "raw": "@eslint/eslintrc@^2.1.4", "name": "@eslint/eslintrc", "escapedName": "@eslint%2feslintrc", "scope": "@eslint", "rawSpec": "^2.1.4", "saveSpec": null, "fetchSpec": "^2.1.4"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.4.tgz", "_shasum": "388a269f0f25c1b6adc317b5a2c55714894c70ad", "_spec": "@eslint/eslintrc@^2.1.4", "_where": "/Users/<USER>/Desktop/test/node_modules/eslint", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/eslint/eslintrc/issues"}, "bundleDependencies": false, "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "deprecated": false, "description": "The legacy ESLintRC config file format for ESLint", "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "eslint": "^7.31.0", "eslint-config-eslint": "^7.0.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-plugin-node": "^11.1.0", "eslint-release": "^3.2.0", "fs-teardown": "^0.1.3", "mocha": "^9.0.3", "rollup": "^2.70.1", "shelljs": "^0.8.4", "sinon": "^11.1.2", "temp-dir": "^2.0.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./lib/index.js", "require": "./dist/eslintrc.cjs"}, "./package.json": "./package.json", "./universal": {"import": "./lib/index-universal.js", "require": "./dist/eslintrc-universal.cjs"}}, "files": ["lib", "conf", "LICENSE", "dist", "universal.js"], "funding": "https://opencollective.com/eslint", "homepage": "https://github.com/eslint/eslintrc#readme", "keywords": ["ESLint", "ESLintRC", "Configuration"], "license": "MIT", "main": "./dist/eslintrc.cjs", "name": "@eslint/eslintrc", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/eslint/eslintrc.git"}, "scripts": {"build": "rollup -c", "lint": "eslint . --report-unused-disable-directives", "lint:fix": "npm run lint -- --fix", "prepare": "npm run build", "release:generate:alpha": "eslint-generate-prerelease alpha", "release:generate:beta": "eslint-generate-prerelease beta", "release:generate:latest": "eslint-generate-release", "release:generate:rc": "eslint-generate-prerelease rc", "release:publish": "eslint-publish-release", "test": "mocha -R progress -c 'tests/lib/*.cjs' && c8 mocha -R progress -c 'tests/lib/**/*.js'"}, "type": "module", "version": "2.1.4"}
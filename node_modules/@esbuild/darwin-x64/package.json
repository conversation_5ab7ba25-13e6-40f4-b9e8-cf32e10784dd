{"_from": "@esbuild/darwin-x64@0.18.20", "_id": "@esbuild/darwin-x64@0.18.20", "_inBundle": false, "_integrity": "sha512-pc5gxlMDxzm513qPGbCbDukOdsGtKhfxD1zJKXjCCcU7ju50O7MeAZ8c4krSJcOIJGFR+qx21yMMVYwiQvyTyQ==", "_location": "/@esbuild/darwin-x64", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@esbuild/darwin-x64@0.18.20", "name": "@esbuild/darwin-x64", "escapedName": "@esbuild%2fdarwin-x64", "scope": "@esbuild", "rawSpec": "0.18.20", "saveSpec": null, "fetchSpec": "0.18.20"}, "_requiredBy": ["/esbuild"], "_resolved": "https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.18.20.tgz", "_shasum": "d70d5790d8bf475556b67d0f8b7c5bdff053d85d", "_spec": "@esbuild/darwin-x64@0.18.20", "_where": "/Users/<USER>/Desktop/test/node_modules/esbuild", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "bundleDependencies": false, "cpu": ["x64"], "deprecated": false, "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "engines": {"node": ">=12"}, "homepage": "https://github.com/evanw/esbuild#readme", "license": "MIT", "name": "@esbuild/darwin-x64", "os": ["darwin"], "preferUnplugged": true, "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "version": "0.18.20"}
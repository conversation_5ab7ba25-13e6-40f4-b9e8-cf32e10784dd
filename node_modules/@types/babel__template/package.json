{"_from": "@types/babel__template@*", "_id": "@types/babel__template@7.4.4", "_inBundle": false, "_integrity": "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==", "_location": "/@types/babel__template", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/babel__template@*", "name": "@types/babel__template", "escapedName": "@types%2fbabel__template", "scope": "@types", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/@types/babel__core"], "_resolved": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz", "_shasum": "5672513701c1b2199bc6dad636a9d7491586766f", "_spec": "@types/babel__template@*", "_where": "/Users/<USER>/Desktop/test/node_modules/@types/babel__core", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/yortus"}, {"name": "<PERSON>", "url": "https://github.com/marvinhagemeister"}, {"name": "<PERSON>", "url": "https://github.com/mgroenhoff"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss"}], "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}, "deprecated": false, "description": "TypeScript definitions for @babel/template", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__template", "license": "MIT", "main": "", "name": "@types/babel__template", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/babel__template"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "5730d754b4d1fcd41676b093f9e32b340c749c4d37b126dfa312e394467e86c6", "version": "7.4.4"}
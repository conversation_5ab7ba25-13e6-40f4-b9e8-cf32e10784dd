{"_from": "@types/babel__core@^7.20.5", "_id": "@types/babel__core@7.20.5", "_inBundle": false, "_integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==", "_location": "/@types/babel__core", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/babel__core@^7.20.5", "name": "@types/babel__core", "escapedName": "@types%2fbabel__core", "scope": "@types", "rawSpec": "^7.20.5", "saveSpec": null, "fetchSpec": "^7.20.5"}, "_requiredBy": ["/@vitejs/plugin-react"], "_resolved": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz", "_shasum": "3df15f27ba85319caa07ba08d0721889bb39c017", "_spec": "@types/babel__core@^7.20.5", "_where": "/Users/<USER>/Desktop/test/node_modules/@vitejs/plugin-react", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/yortus"}, {"name": "<PERSON>", "url": "https://github.com/marvinhagemeister"}, {"name": "<PERSON>", "url": "https://github.com/mgroenhoff"}, {"name": "<PERSON>", "url": "https://github.com/Jessidhia"}, {"name": "<PERSON><PERSON><PERSON> Jr.", "url": "https://github.com/ifiokjr"}], "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}, "deprecated": false, "description": "TypeScript definitions for @babel/core", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core", "license": "MIT", "main": "", "name": "@types/babel__core", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/babel__core"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "3ece429b02ff9f70503a5644f2b303b04d10e6da7940c91a9eff5e52f2c76b91", "version": "7.20.5"}
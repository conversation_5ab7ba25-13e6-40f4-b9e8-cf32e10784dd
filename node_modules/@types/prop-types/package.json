{"_from": "@types/prop-types@*", "_id": "@types/prop-types@15.7.15", "_inBundle": false, "_integrity": "sha512-F6bEyamV9jKGAFBEmlQnesRPGOQqS2+Uwi0Em15xenOxHaf2hv6L8YCVn3rPdPJOiJfPiCnLIRyvwVaqMY3MIw==", "_location": "/@types/prop-types", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/prop-types@*", "name": "@types/prop-types", "escapedName": "@types%2fprop-types", "scope": "@types", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/@types/react"], "_resolved": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.15.tgz", "_shasum": "e6e5a86d602beaca71ce5163fadf5f95d70931c7", "_spec": "@types/prop-types@*", "_where": "/Users/<USER>/Desktop/test/node_modules/@types/react", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ferdaber"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for prop-types", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/prop-types", "license": "MIT", "main": "", "name": "@types/prop-types", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/prop-types"}, "scripts": {}, "typeScriptVersion": "5.1", "types": "index.d.ts", "typesPublisherContentHash": "92a20bc6f48f988ae6f314daa592e457e4b7ccb6ef115535bf69c7061375a248", "version": "15.7.15"}
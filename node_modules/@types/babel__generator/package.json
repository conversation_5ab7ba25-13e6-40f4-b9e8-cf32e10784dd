{"_from": "@types/babel__generator@*", "_id": "@types/babel__generator@7.27.0", "_inBundle": false, "_integrity": "sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg==", "_location": "/@types/babel__generator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/babel__generator@*", "name": "@types/babel__generator", "escapedName": "@types%2fbabel__generator", "scope": "@types", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/@types/babel__core"], "_resolved": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz", "_shasum": "b5819294c51179957afaec341442f9341e4108a9", "_spec": "@types/babel__generator@*", "_where": "/Users/<USER>/Desktop/test/node_modules/@types/babel__core", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/yortus"}, {"name": "<PERSON>", "url": "https://github.com/mgroenhoff"}, {"name": "<PERSON>", "url": "https://github.com/khell"}, {"name": "Lyanbin", "url": "https://github.com/Lyanbin"}], "dependencies": {"@babel/types": "^7.0.0"}, "deprecated": false, "description": "TypeScript definitions for @babel/generator", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__generator", "license": "MIT", "main": "", "name": "@types/babel__generator", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/babel__generator"}, "scripts": {}, "typeScriptVersion": "5.1", "types": "index.d.ts", "typesPublisherContentHash": "b5c7deac65dbd6ab9b313d1d71c86afe4383b881dcb4e3b3ac51dab07b8f95fb", "version": "7.27.0"}
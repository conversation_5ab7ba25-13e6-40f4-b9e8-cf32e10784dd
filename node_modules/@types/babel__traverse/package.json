{"_from": "@types/babel__traverse@*", "_id": "@types/babel__traverse@7.20.7", "_inBundle": false, "_integrity": "sha512-dkO5fhS7+/oos4ciWxyEyjWe48zmG6wbCheo/G2ZnHx4fs3EU6YC6UM8rk56gAjNJ9P3MTH2jo5jb92/K6wbng==", "_location": "/@types/babel__traverse", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/babel__traverse@*", "name": "@types/babel__traverse", "escapedName": "@types%2fbabel__traverse", "scope": "@types", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/@types/babel__core"], "_resolved": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "_shasum": "968cdc2366ec3da159f61166428ee40f370e56c2", "_spec": "@types/babel__traverse@*", "_where": "/Users/<USER>/Desktop/test/node_modules/@types/babel__core", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/yortus"}, {"name": "<PERSON>", "url": "https://github.com/marvinhagemeister"}, {"name": "<PERSON>", "url": "https://github.com/rpetrich"}, {"name": "<PERSON>", "url": "https://github.com/mgroenhoff"}, {"name": "<PERSON>", "url": "https://github.com/dlgrit"}, {"name": "<PERSON><PERSON><PERSON> Jr.", "url": "https://github.com/ifiokjr"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON>", "url": "https://github.com/danez"}], "dependencies": {"@babel/types": "^7.20.7"}, "deprecated": false, "description": "TypeScript definitions for @babel/traverse", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "license": "MIT", "main": "", "name": "@types/babel__traverse", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/babel__traverse"}, "scripts": {}, "typeScriptVersion": "5.0", "types": "index.d.ts", "typesPublisherContentHash": "e58d29a4d5c39ba4fa0291c8c7d5abad18881f7ed9f938feeb97726ad48a0544", "version": "7.20.7"}
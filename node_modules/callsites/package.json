{"_from": "callsites@^3.0.0", "_id": "callsites@3.1.0", "_inBundle": false, "_integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==", "_location": "/callsites", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "callsites@^3.0.0", "name": "callsites", "escapedName": "callsites", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/parent-module"], "_resolved": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz", "_shasum": "b3630abd8943432f54b3f0519238e33cd7df2f73", "_spec": "callsites@^3.0.0", "_where": "/Users/<USER>/Desktop/test/node_modules/parent-module", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/callsites/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Get callsites from the V8 stack trace API", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/callsites#readme", "keywords": ["stacktrace", "v8", "callsite", "callsites", "stack", "trace", "function", "file", "line", "debug"], "license": "MIT", "name": "callsites", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/callsites.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "3.1.0"}
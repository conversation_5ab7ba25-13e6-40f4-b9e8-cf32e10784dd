{"_from": "@ampproject/remapping@^2.2.0", "_id": "@ampproject/remapping@2.3.0", "_inBundle": false, "_integrity": "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==", "_location": "/@ampproject/remapping", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@ampproject/remapping@^2.2.0", "name": "@ampproject/remapping", "escapedName": "@ampproject%2fremapping", "scope": "@ampproject", "rawSpec": "^2.2.0", "saveSpec": null, "fetchSpec": "^2.2.0"}, "_requiredBy": ["/@babel/core"], "_resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "_shasum": "ed441b6fa600072520ce18b43d2c8cc8caecc7f4", "_spec": "@ampproject/remapping@^2.2.0", "_where": "/Users/<USER>/Desktop/test/node_modules/@babel/core", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "bundleDependencies": false, "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "deprecated": false, "description": "Remap sequential sourcemaps through transformations to point at the original source code", "devDependencies": {"@rollup/plugin-typescript": "8.3.2", "@types/jest": "27.4.1", "@typescript-eslint/eslint-plugin": "5.20.0", "@typescript-eslint/parser": "5.20.0", "eslint": "8.14.0", "eslint-config-prettier": "8.5.0", "jest": "27.5.1", "jest-config": "27.5.1", "npm-run-all": "4.1.5", "prettier": "2.6.2", "rollup": "2.70.2", "ts-jest": "27.1.4", "tslib": "2.4.0", "typescript": "4.6.3"}, "engines": {"node": ">=6.0.0"}, "exports": {".": [{"types": "./dist/types/remapping.d.ts", "browser": "./dist/remapping.umd.js", "require": "./dist/remapping.umd.js", "import": "./dist/remapping.mjs"}, "./dist/remapping.umd.js"], "./package.json": "./package.json"}, "files": ["dist"], "homepage": "https://github.com/ampproject/remapping#readme", "keywords": ["source", "map", "remap"], "license": "Apache-2.0", "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "name": "@ampproject/remapping", "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint test:only", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "types": "dist/types/remapping.d.ts", "version": "2.3.0"}
{"_from": "word-wrap@^1.2.5", "_id": "word-wrap@1.2.5", "_inBundle": false, "_integrity": "sha512-B<PERSON>22B<PERSON>eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==", "_location": "/word-wrap", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "word-wrap@^1.2.5", "name": "word-wrap", "escapedName": "word-wrap", "rawSpec": "^1.2.5", "saveSpec": null, "fetchSpec": "^1.2.5"}, "_requiredBy": ["/optionator"], "_resolved": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz", "_shasum": "d2c45c6dd4fbce621a66f136cbe328afd0410b34", "_spec": "word-wrap@^1.2.5", "_where": "/Users/<USER>/Desktop/test/node_modules/optionator", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "localhost:8080"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://2fd.github.io"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/hildjj"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "https://tck.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/lordvlad"}, {"name": "<PERSON>", "url": "http://www.linestarve.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://zachhale.com"}], "deprecated": false, "description": "Wrap words to a specified length.", "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/jonschlinkert/word-wrap", "keywords": ["break", "carriage", "line", "new-line", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "license": "MIT", "main": "index.js", "name": "word-wrap", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/word-wrap.git"}, "scripts": {"test": "mocha"}, "typings": "index.d.ts", "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["common-words", "shuffle-words", "unique-words", "wordcount"]}, "reflinks": ["verb", "verb-generate-readme"]}, "version": "1.2.5"}
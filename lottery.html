<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎰 Lottery Number Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            width: 100%;
            text-align: center;
        }

        .header {
            margin-bottom: 40px;
        }

        .header h1 {
            color: #4CAF50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 1.2rem;
        }

        .probability-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .probability-info h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .odds {
            font-size: 1.1rem;
            font-weight: bold;
            margin: 10px 0;
        }

        .percentage {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .number-display {
            margin-bottom: 30px;
        }

        .main-numbers-section,
        .lucky-number-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 20px;
            border: 2px solid #e9ecef;
        }

        .section-title {
            color: #333;
            font-size: 1.3rem;
            margin-bottom: 15px;
        }

        .numbers-container {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .lucky-container {
            display: flex;
            justify-content: center;
        }

        .number-ball {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
            background: linear-gradient(135deg, #FF6B6B, #FF8E53);
        }

        .number-ball:hover {
            transform: scale(1.1);
        }

        .lucky-number {
            background: linear-gradient(135deg, #4ECDC4, #44A08D) !important;
            width: 80px;
            height: 80px;
            font-size: 2rem;
        }

        .placeholder {
            color: #999;
            font-style: italic;
            padding: 20px;
            font-size: 1.1rem;
        }

        .generate-btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2rem;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
            font-weight: bold;
            margin-bottom: 20px;
        }

        .generate-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .generate-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .generate-btn.generating {
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .results-summary {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
            display: none;
        }

        .results-summary h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .number-summary {
            font-size: 1.1rem;
            font-weight: bold;
            color: #555;
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .numbers-container {
                gap: 10px;
            }
            
            .number-ball {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }
            
            .lucky-number {
                width: 70px;
                height: 70px;
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎰 Lottery Number Generator</h1>
            <p class="subtitle">Generate your lucky numbers!</p>
        </div>

        <div class="probability-info">
            <h3>Game Rules</h3>
            <p>Pick 5 numbers from 1-48 + 1 lucky number from 1-18</p>
            <p class="odds">Odds of winning: 1 in 30,821,472</p>
            <p class="percentage">That's a 0.00000324% chance!</p>
        </div>

        <div class="number-display">
            <div class="main-numbers-section">
                <h3 class="section-title">Main Numbers (1-48)</h3>
                <div class="numbers-container" id="mainNumbers">
                    <div class="placeholder">Click generate to see your numbers!</div>
                </div>
            </div>

            <div class="lucky-number-section">
                <h3 class="section-title">Lucky Number (1-18)</h3>
                <div class="lucky-container" id="luckyNumber">
                    <div class="placeholder">?</div>
                </div>
            </div>
        </div>

        <button class="generate-btn" id="generateBtn" onclick="generateNumbers()">
            🎲 Generate Numbers
        </button>

        <div class="results-summary" id="resultsSummary">
            <h4>Your Numbers:</h4>
            <p class="number-summary" id="numberSummary"></p>
        </div>
    </div>

    <script>
        let isGenerating = false;

        // Function to generate random numbers without duplicates
        function generateUniqueNumbers(min, max, count) {
            const numbers = new Set();
            while (numbers.size < count) {
                const randomNum = Math.floor(Math.random() * (max - min + 1)) + min;
                numbers.add(randomNum);
            }
            return Array.from(numbers).sort((a, b) => a - b);
        }

        // Function to generate a single random number
        function generateSingleNumber(min, max) {
            return Math.floor(Math.random() * (max - min + 1)) + min;
        }

        // Main function to generate lottery numbers
        function generateNumbers() {
            if (isGenerating) return;
            
            isGenerating = true;
            const btn = document.getElementById('generateBtn');
            btn.classList.add('generating');
            btn.disabled = true;
            btn.textContent = '🎲 Generating...';
            
            // Add some animation delay for better UX
            setTimeout(() => {
                const mainNumbers = generateUniqueNumbers(1, 48, 5);
                const luckyNumber = generateSingleNumber(1, 18);
                
                displayNumbers(mainNumbers, luckyNumber);
                
                isGenerating = false;
                btn.classList.remove('generating');
                btn.disabled = false;
                btn.textContent = '🎲 Generate Numbers';
            }, 500);
        }

        // Function to display the generated numbers
        function displayNumbers(mainNumbers, luckyNumber) {
            // Display main numbers
            const mainContainer = document.getElementById('mainNumbers');
            mainContainer.innerHTML = '';
            
            mainNumbers.forEach(number => {
                const ball = document.createElement('div');
                ball.className = 'number-ball';
                ball.textContent = number;
                mainContainer.appendChild(ball);
            });

            // Display lucky number
            const luckyContainer = document.getElementById('luckyNumber');
            luckyContainer.innerHTML = '';
            
            const luckyBall = document.createElement('div');
            luckyBall.className = 'number-ball lucky-number';
            luckyBall.textContent = luckyNumber;
            luckyContainer.appendChild(luckyBall);

            // Show results summary
            const resultsSummary = document.getElementById('resultsSummary');
            const numberSummary = document.getElementById('numberSummary');
            
            numberSummary.textContent = `Main: ${mainNumbers.join(', ')} | Lucky: ${luckyNumber}`;
            resultsSummary.style.display = 'block';
        }

        // Add some fun interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Add keyboard support
            document.addEventListener('keydown', function(event) {
                if (event.code === 'Space' || event.code === 'Enter') {
                    event.preventDefault();
                    generateNumbers();
                }
            });
        });
    </script>
</body>
</html>
